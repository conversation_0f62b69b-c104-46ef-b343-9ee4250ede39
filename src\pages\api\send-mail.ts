import { NextApiRequest, NextApiResponse } from 'next';
import { Resend } from 'resend';
import { z } from 'zod';

// Initialiser Resend avec votre clé API
const resend = new Resend(process.env.RESEND_API_KEY);

// Schéma de validation pour les données reçues
const contactRequestSchema = z.object({
  name: z.string().min(2),
  surname: z.string().min(2),
  email: z.email(),
  phone: z.string().min(8),
  company: z.string().optional(),
  role: z.string().optional(),
  serviceType: z.string().min(1),
  description: z.string().min(10),
  budget: z.string().optional(),
  attachments: z
    .array(
      z.object({
        content: z.string(), // Base64 string
        filename: z.string(),
        content_type: z.string(),
      }),
    )
    .optional(),
});

type ContactRequest = z.infer<typeof contactRequestSchema>;

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse,
) {
  // Vérifier que la méthode est POST
  if (req.method !== 'POST') {
    return res.status(405).json({
      error: 'Méthode non autorisée. Utilisez POST.',
    });
  }

  try {
    // Valider les données reçues
    const validatedData = contactRequestSchema.parse(req.body);

    // Préparer les pièces jointes pour Resend
    const attachments =
      validatedData.attachments?.map((file) => ({
        content: file.content,
        filename: file.filename,
        content_type: file.content_type,
      })) || [];

    // Créer le contenu HTML de l'email
    const emailHtml = createEmailHtml(validatedData);

    // Envoyer l'email avec Resend
    const emailResponse = await resend.emails.send({
      from: `${validatedData.name} ${validatedData.surname} <<EMAIL>>`, // Remplacez par votre domaine vérifié
      to: ['<EMAIL>'], // Email de destination
      subject: `Nouvelle demande de contact - ${validatedData.serviceType}`,
      html: emailHtml,
      attachments: attachments.length > 0 ? attachments : undefined,
      replyTo: validatedData.email,
    });

    // Envoyer un email de confirmation au client
    const confirmationHtml = createConfirmationEmailHtml(validatedData);

    await resend.emails.send({
      from: '<EMAIL>',
      to: [validatedData.email],
      subject: 'Confirmation de réception - HREFF SARL',
      html: confirmationHtml,
    });

    if (emailResponse.error) {
      return res.status(500).json({
        error: "Erreur lors de l'envoi de l'email",
        message: emailResponse.error.message,
      });
    }
    console.log('Email envoyé avec succès:', emailResponse);

    return res.status(200).json({
      success: true,
      message: 'Email envoyé avec succès',
      id: emailResponse.data?.id,
    });
  } catch (error) {
    console.error("Erreur lors de l'envoi de l'email:", error);

    if (error instanceof z.ZodError) {
      return res.status(400).json({
        error: 'Données invalides',
        details: z.prettifyError(error),
      });
    }

    return res.status(500).json({
      error: 'Erreur interne du serveur',
      message: "Une erreur est survenue lors de l'envoi de l'email",
    });
  }
}

// Fonction pour créer le HTML de l'email principal
function createEmailHtml(data: ContactRequest): string {
  const serviceTypeLabels: Record<string, string> = {
    'developpement-web': 'Développement Web',
    'developpement-mobile': 'Développement Mobile',
    'logiciel-sur-mesure': 'Logiciel sur Mesure',
    'audit-systeme': 'Audit des Systèmes IT',
    'maintenance-it': 'Maintenance IT',
    ecommerce: 'Solution E-commerce',
    autre: 'Autre',
  };

  return `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <title>Nouvelle demande de contact</title>
      <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background-color: #007bff; color: white; padding: 20px; text-align: center; }
        .content { padding: 20px; background-color: #f8f9fa; }
        .field { margin-bottom: 15px; }
        .label { font-weight: bold; color: #495057; }
        .value { margin-top: 5px; }
        .footer { text-align: center; padding: 20px; color: #6c757d; font-size: 14px; }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <h1>Nouvelle demande de contact</h1>
          <p>HREFF SARL - Solutions Digitales</p>
        </div>
        
        <div class="content">
          <h2>Informations du contact</h2>
          
          <div class="field">
            <div class="label">Nom complet:</div>
            <div class="value">${data.name} ${data.surname}</div>
          </div>
          
          <div class="field">
            <div class="label">Email:</div>
            <div class="value">${data.email}</div>
          </div>
          
          <div class="field">
            <div class="label">Téléphone:</div>
            <div class="value">${data.phone}</div>
          </div>
          
          ${
            data.company
              ? `
          <div class="field">
            <div class="label">Entreprise:</div>
            <div class="value">${data.company}</div>
          </div>
          `
              : ''
          }
          
          ${
            data.role
              ? `
          <div class="field">
            <div class="label">Fonction:</div>
            <div class="value">${data.role}</div>
          </div>
          `
              : ''
          }
          
          <div class="field">
            <div class="label">Type de service:</div>
            <div class="value">${serviceTypeLabels[data.serviceType] || data.serviceType}</div>
          </div>
          
          <div class="field">
            <div class="label">Description du projet:</div>
            <div class="value">${data.description.replace(/\n/g, '<br>')}</div>
          </div>
          
          ${
            data.budget
              ? `
          <div class="field">
            <div class="label">Budget estimé:</div>
            <div class="value">${data.budget} FCFA</div>
          </div>
          `
              : ''
          }
          
          ${
            data.attachments && data.attachments.length > 0
              ? `
          <div class="field">
            <div class="label">Fichiers joints:</div>
            <div class="value">
              ${data.attachments.map((file) => `<p>📎 ${file.filename}</p>`).join('')}
            </div>
          </div>
          `
              : ''
          }
        </div>
        
        <div class="footer">
          <p>Email envoyé depuis le formulaire de contact de hreff.com</p>
          <p>HREFF SARL - Douala, Cameroun</p>
        </div>
      </div>
    </body>
    </html>
  `;
}

// Fonction pour créer l'email de confirmation
function createConfirmationEmailHtml(data: ContactRequest): string {
  return `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <title>Confirmation de réception</title>
      <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background-color: #28a745; color: white; padding: 20px; text-align: center; }
        .content { padding: 20px; background-color: #f8f9fa; }
        .footer { text-align: center; padding: 20px; color: #6c757d; font-size: 14px; }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <h1>Merci pour votre demande !</h1>
          <p>HREFF SARL</p>
        </div>
        
        <div class="content">
          <p>Bonjour ${data.name} ${data.surname},</p>
          
          <p>Nous avons bien reçu votre demande concernant <strong>${data.serviceType}</strong>.</p>
          
          <p>Notre équipe va examiner votre projet et vous répondra dans les plus brefs délais, généralement sous 24 heures.</p>
          
          <p>En attendant, n'hésitez pas à nous contacter directement :</p>
          <ul>
            <li>📞 +237 686 87 68 73</li>
            <li>📧 <EMAIL></li>
            <li>📍 Douala, Cameroun</li>
          </ul>
          
          <p>Cordialement,<br>
          L'équipe HREFF SARL</p>
        </div>
        
        <div class="footer">
          <p>HREFF SARL - Solutions Digitales sur Mesure</p>
          <p>Votre partenaire pour la transformation digitale</p>
        </div>
      </div>
    </body>
    </html>
  `;
}
