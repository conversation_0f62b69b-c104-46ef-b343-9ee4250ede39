import { GetStaticPaths, GetStaticProps } from 'next';
import { getProjectData, getSortedProjectsData } from '@/src/lib/projects';
import { PostFull } from '@/src/lib/type';
import PageBanner from '@/src/components/PageBanner';
import Layouts from '@/src/layouts/Layouts';
import Link from 'next/link';
import Image from 'next/image';

interface ProjectPageProps {
  project: PostFull;
}

const ProjectPage = ({ project }: ProjectPageProps) => {
  return (
    <Layouts>
      <PageBanner
        pageName={project.title as string}
        pageTitle={project.title as string}
      />
      <section className="container mb-5 mt-20">
        <div className="grid grid-cols-[1fr] lg:grid-cols-[auto_40px_500px]">
          <div className="prose prose-sm">
            {/* project content */}
            <div
              className="mil-project-content"
              dangerouslySetInnerHTML={{ __html: project.contentHtml }}
            />
          </div>
          <div className="mr-8 hidden h-full w-0.5 bg-orange-400 lg:block" />
          <div className="flex flex-col gap-3">
            <div className="hidden overflow-hidden rounded-md border lg:block">
              <Image
                src={`/${project.image}`}
                alt={project.title as string}
                width={500}
                height={300}
              />
            </div>

            <div className="mil-sidebar">
              <div className="mil-widget mil-mb-60">
                <ul className="mil-simple-list text-sm">
                  <li>
                    <strong>Client: </strong> {project.author}
                  </li>
                  <li>
                    <strong>Catégorie: </strong> {project.tags?.join(', ')}
                  </li>
                  <li>
                    <strong>Statut: </strong> Terminé
                  </li>
                </ul>
              </div>

              {/* project summary */}
              <div className="mil-widget mil-mb-60">
                <h5 className="mil-mb-30">Résumé</h5>
                <p>{project.short}</p>
              </div>

              {/* call to action */}
              <div className="mil-widget">
                <h5 className="mil-mb-30">
                  Intéressé par un projet similaire ?
                </h5>
                <p className="mil-mb-30">
                  Contactez-nous pour discuter de vos besoins et découvrir
                  comment nous pouvons vous aider.
                </p>
                <Link href="/contact" className="mil-button mil-border">
                  <span>Nous Contacter</span>
                </Link>
              </div>
            </div>
          </div>
        </div>
      </section>
    </Layouts>
  );
};

export default ProjectPage;

export const getStaticPaths: GetStaticPaths = async () => {
  const projects = getSortedProjectsData();
  const paths = projects.map((project) => ({
    params: { slug: project.id },
  }));

  return {
    paths,
    fallback: false,
  };
};

export const getStaticProps: GetStaticProps = async ({ params }) => {
  const project = await getProjectData(params!.slug as string);

  return {
    props: {
      project,
    },
  };
};
