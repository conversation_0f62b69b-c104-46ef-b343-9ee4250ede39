# Formulaire de Contact Amélioré - HREFF SARL

## Vue d'ensemble

Le formulaire de contact a été amélioré avec les technologies suivantes :
- **Zod** pour la validation des données
- **React Hook Form** pour la gestion du formulaire
- **Resend** pour l'envoi d'emails
- Support des fichiers joints (images, PDF, DOC)

## Fonctionnalités

### Validation des données
- Validation en temps réel avec Zod
- Messages d'erreur personnalisés en français
- Champs obligatoires marqués avec un astérisque (*)

### Gestion des fichiers
- Support de plusieurs fichiers
- Types autorisés : Images (JPEG, PNG, GIF, WebP), PDF, DOC, DOCX
- Taille maximale : 20MB par fichier
- Conversion automatique en Base64 pour l'envoi

### Envoi d'emails
- Email principal envoyé à l'équipe HREFF
- Email de confirmation automatique au client
- Gestion des pièces jointes
- Templates HTML professionnels

## Configuration

### 1. Variables d'environnement

Créez un fichier `.env.local` basé sur `.env.example` :

```bash
cp .env.example .env.local
```

Configurez les variables :
```env
RESEND_API_KEY=re_xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
EMAIL_FROM_DOMAIN=hreff.com
EMAIL_TO=<EMAIL>
```

### 2. Configuration Resend

1. Créez un compte sur [Resend](https://resend.com/)
2. Vérifiez votre domaine
3. Obtenez votre clé API
4. Ajoutez la clé dans `.env.local`

## Structure des données

### Schéma de validation (Zod)

```typescript
const contactSchema = z.object({
  name: z.string().min(2, 'Le nom doit contenir au moins 2 caractères'),
  surname: z.string().min(2, 'Le prénom doit contenir au moins 2 caractères'),
  email: z.string().email('Adresse email invalide'),
  phone: z.string().min(8, 'Numéro de téléphone invalide'),
  company: z.string().optional(),
  role: z.string().optional(),
  serviceType: z.string().min(1, 'Veuillez sélectionner un type de service'),
  description: z.string().min(10, 'La description doit contenir au moins 10 caractères'),
  budget: z.string().optional(),
  files: z.array(z.instanceof(File)).optional(),
});
```

### Format des fichiers joints

Les fichiers sont envoyés au format suivant :
```typescript
interface AttachmentFile {
  content: string;      // Contenu en Base64
  filename: string;     // Nom du fichier
  content_type: string; // Type MIME
}
```

## API Endpoint

### POST `/api/send-mail/`

Envoie un email avec les données du formulaire et les pièces jointes.

**Corps de la requête :**
```json
{
  "name": "Jean",
  "surname": "Dupont",
  "email": "<EMAIL>",
  "phone": "+237 6XX XX XX XX",
  "company": "Entreprise ABC",
  "role": "Directeur",
  "serviceType": "developpement-web",
  "description": "Description du projet...",
  "budget": "1000000",
  "attachments": [
    {
      "content": "base64_content_here",
      "filename": "document.pdf",
      "content_type": "application/pdf"
    }
  ]
}
```

**Réponse de succès :**
```json
{
  "success": true,
  "message": "Email envoyé avec succès",
  "id": "email_id"
}
```

**Réponse d'erreur :**
```json
{
  "error": "Description de l'erreur",
  "details": "Détails supplémentaires"
}
```

## Styles CSS

Les styles pour le formulaire sont définis dans `src/styles/globals.css` :
- Messages d'erreur en rouge
- États d'erreur pour les champs
- Alertes de succès/erreur
- Styles pour l'affichage des fichiers sélectionnés

## Types de services disponibles

- Développement Web
- Développement Mobile
- Logiciel sur Mesure
- Audit des Systèmes IT
- Maintenance IT
- Solution E-commerce
- Autre

## Sécurité

- Validation côté serveur avec Zod
- Vérification des types de fichiers
- Limitation de la taille des fichiers
- Protection contre les injections XSS dans les emails

## Déploiement

1. Assurez-vous que toutes les variables d'environnement sont configurées
2. Vérifiez votre domaine dans Resend
3. Testez l'envoi d'emails en développement
4. Déployez l'application

## Dépannage

### Erreurs courantes

1. **"RESEND_API_KEY not found"**
   - Vérifiez que la variable d'environnement est définie
   - Redémarrez le serveur de développement

2. **"Domain not verified"**
   - Vérifiez votre domaine dans le dashboard Resend
   - Utilisez un domaine vérifié pour `from`

3. **"File too large"**
   - Vérifiez que les fichiers font moins de 20MB
   - Optimisez les images avant upload

4. **Erreurs de validation**
   - Vérifiez que tous les champs obligatoires sont remplis
   - Consultez les messages d'erreur affichés

## Support

Pour toute question ou problème :
- Email : <EMAIL>
- Téléphone : +237 686 87 68 73
